#!/usr/bin/env python3
"""
Test script per verificare la connessione ai cavi spare
"""

import requests
import json

def test_spare_cables():
    """Test della connessione ai cavi spare"""
    
    print("🧪 Test connessione cavi spare")
    print("=" * 50)
    
    # URL del backend
    base_url = "http://localhost:8001/api"
    
    # Test 1: Health check
    print("🔍 Test 1: Health check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Backend raggiungibile")
            print(f"   Risposta: {response.json()}")
        else:
            print(f"❌ Backend non raggiungibile: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Errore connessione backend: {e}")
        return False
    
    # Test 2: Login per ottenere token
    print("\n🔐 Test 2: Login...")
    try:
        # Usa form data per OAuth2
        login_data = {
            "username": "admin",
            "password": "admin"
        }
        response = requests.post(f"{base_url}/auth/login", data=login_data, timeout=10)
        if response.status_code == 200:
            token = response.json().get("access_token")
            print("✅ Login riuscito")
            print(f"   Token: {token[:50]}...")
        else:
            print(f"❌ Login fallito: {response.status_code}")
            print(f"   Risposta: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Errore login: {e}")
        return False
    
    # Test 3: Test endpoint cavi spare
    print("\n📋 Test 3: Endpoint cavi spare...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        cantiere_id = 1
        
        response = requests.get(f"{base_url}/cavi/spare/{cantiere_id}", headers=headers, timeout=10)
        print(f"   URL: {base_url}/cavi/spare/{cantiere_id}")
        print(f"   Status code: {response.status_code}")
        
        if response.status_code == 200:
            cavi_spare = response.json()
            print(f"✅ Endpoint cavi spare funzionante")
            print(f"   Cavi spare trovati: {len(cavi_spare)}")
            
            if len(cavi_spare) > 0:
                print("   Primi 3 cavi spare:")
                for i, cavo in enumerate(cavi_spare[:3]):
                    print(f"     {i+1}. ID: {cavo.get('id_cavo', 'N/A')}, Tipologia: {cavo.get('tipologia', 'N/A')}")
            else:
                print("   Nessun cavo spare trovato (normale se non ci sono cavi spare)")
                
        elif response.status_code == 404:
            print(f"❌ Cantiere non trovato: {response.json()}")
            return False
        else:
            print(f"❌ Errore endpoint: {response.status_code}")
            print(f"   Risposta: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore test endpoint: {e}")
        return False
    
    # Test 4: Test endpoint cavi standard con filtro spare
    print("\n📋 Test 4: Endpoint cavi standard con filtro spare...")
    try:
        response = requests.get(f"{base_url}/cavi/{cantiere_id}?tipo_cavo=3", headers=headers, timeout=10)
        print(f"   URL: {base_url}/cavi/{cantiere_id}?tipo_cavo=3")
        print(f"   Status code: {response.status_code}")
        
        if response.status_code == 200:
            cavi_spare_std = response.json()
            print(f"✅ Endpoint cavi standard con filtro funzionante")
            print(f"   Cavi spare trovati: {len(cavi_spare_std)}")
        else:
            print(f"❌ Errore endpoint standard: {response.status_code}")
            print(f"   Risposta: {response.text}")
            
    except Exception as e:
        print(f"❌ Errore test endpoint standard: {e}")
    
    print("\n✅ Test completato con successo!")
    return True

if __name__ == "__main__":
    test_spare_cables()
